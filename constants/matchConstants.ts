/**
 * Constants for match-related components and functionality
 */

// Action colors for match actions
export const ACTION_COLORS = {
  EDIT: 'gray-400',
  DELETE: 'red-500',
  CANCEL: 'text-typography-700',
  UPDATE_SCORE: 'text-typography-700',
  WALKOVER: 'text-typography-700',
} as const;

// Scroll and interaction thresholds
export const INTERACTION_THRESHOLDS = {
  SCROLL_THRESHOLD: 10, // pixels
  TIME_THRESHOLD: 300, // milliseconds
} as const;

// Cache settings
export const CACHE_SETTINGS = {
  DEFAULT_EXPIRY_MS: 5 * 60 * 1000, // 5 minutes
} as const;

// Match action labels
export const MATCH_ACTION_LABELS = {
  CANCEL: 'Cancel match',
  CANCELLING: 'Cancelling...',
  UPDATE_SCORE: 'Update Final score',
  WALKOVER: 'Mark as Walkover',
  START_MATCH: 'Slide to Start Match',
  MATCH_ACTIONS: 'MATCH ACTIONS',
} as const;

// Date format patterns
export const DATE_FORMATS = {
  DISPLAY_DATE: 'MMM DD, YYYY',
  DISPLAY_TIME: 'h:mm A',
  DETAILED_DATE: 'MMM Do, YYYY',
} as const;

// Component sizing
export const COMPONENT_SIZES = {
  SLIDE_BUTTON_HEIGHT: 52,
  MAX_PARTICIPANT_WIDTH: '25%',
  CENTER_SECTION_WIDTH: '50%',
} as const;
