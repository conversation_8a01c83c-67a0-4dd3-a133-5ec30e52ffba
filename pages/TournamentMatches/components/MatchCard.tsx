import React, { memo, useState } from "react";
import { View, Pressable } from "react-native";
import { useScrollAwareTap } from "../../../hooks/useScrollAwareTap";
import { Card } from "@/components/ui/card";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Match } from "../../../types/matches";
import ParticipantDisplay from "./ParticipantDisplay";
import MatchDetailsDialog from "./MatchDetailsDialog";
import CourtFieldDisplay from "./CourtFieldDisplay";
import MatchStatusDisplay from "./MatchStatusDisplay";
import { useMatchParticipants } from "../../../hooks/useMatchParticipants";
import {
  INTERACTION_THRESHOLDS,
  COMPONENT_SIZES,
} from "@/constants/matchConstants";

export interface MatchCardProps {
  match: Match;
  onMatchDeleted?: () => void;
}

const MatchCard: React.FC<MatchCardProps> = ({ match, onMatchDeleted }) => {
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  const { participant1, participant2 } = useMatchParticipants({
    participant1Id: match.participant_1_id,
    participant2Id: match.participant_2_id,
    participantType: match.participant_type,
    participant1Name: match.participant_1_name,
    participant2Name: match.participant_2_name,
  });

  const handlePress = () => {
    setIsDetailsDialogOpen(true);
  };

  const { onTouchStart, onTouchEnd, onTouchCancel } = useScrollAwareTap({
    onTap: handlePress,
    scrollThreshold: INTERACTION_THRESHOLDS.SCROLL_THRESHOLD,
    timeThreshold: INTERACTION_THRESHOLDS.TIME_THRESHOLD,
  });

  return (
    <>
      <Pressable
        onTouchStart={onTouchStart}
        onTouchEnd={onTouchEnd}
        onTouchCancel={onTouchCancel}
        onPress={undefined} // Disable default onPress to avoid conflicts
      >
        <Card className="bg-white border border-gray-200 rounded-3xl shadow-md">
          <VStack className="p-2">
            {/* Participants Section - Opposite Sides with Centered Date/Time */}
            <HStack className="items-center w-full">
              <View
                className={`flex-1 max-w-[${COMPONENT_SIZES.MAX_PARTICIPANT_WIDTH}]`}
              >
                <ParticipantDisplay
                  participantId={match.participant_1_id}
                  participantType={match.participant_type}
                  participantName={match.participant_1_name}
                  fallbackName="TBD"
                  preloadedParticipant={participant1}
                />
              </View>

              <MatchStatusDisplay
                scheduledDate={match.scheduled_date}
                status={match.status}
              />

              <View
                className={`flex-1 max-w-[${COMPONENT_SIZES.MAX_PARTICIPANT_WIDTH}]`}
              >
                <ParticipantDisplay
                  participantId={match.participant_2_id}
                  participantType={match.participant_type}
                  participantName={match.participant_2_name}
                  fallbackName="TBD"
                  preloadedParticipant={participant2}
                />
              </View>
            </HStack>
            <CourtFieldDisplay courtFieldNumber={match.court_field_number} />
          </VStack>
        </Card>
      </Pressable>
      {isDetailsDialogOpen && (
        <MatchDetailsDialog
          isOpen={isDetailsDialogOpen}
          onClose={() => setIsDetailsDialogOpen(false)}
          match={match}
          participant1Details={participant1}
          participant2Details={participant2}
          onMatchDeleted={onMatchDeleted}
        />
      )}
    </>
  );
};

export default memo(MatchCard);
