import React from 'react';
import { Modal, ModalBackdrop, ModalContent } from '@/components/ui/modal';
import ConfirmationPrompt from '@/components/k-components/ConfirmationPrompt';
import { type Match } from '@/types/matches';

interface MatchDeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  match: Match | null;
  onDeleteConfirm: () => Promise<void>;
  loading?: boolean;
}

const MatchDeleteConfirmationModal: React.FC<
  MatchDeleteConfirmationModalProps
> = ({ isOpen, onClose, match, onDeleteConfirm, loading = false }) => {
  const getMatchDisplayName = () => {
    if (!match) return 'this match';
    
    const participant1 = match.participant_1_name || 'TBD';
    const participant2 = match.participant_2_name || 'TBD';
    
    return `${participant1} vs ${participant2}`;
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalBackdrop />
      <ModalContent className="p-6">
        <ConfirmationPrompt
          heading="Delete Match?"
          description={`Are you sure you want to delete ${getMatchDisplayName()}? This action cannot be undone.`}
          primaryText="Delete"
          secondaryText="Cancel"
          loading={loading}
          onPrimaryPress={onDeleteConfirm}
          onSecondaryPress={onClose}
          type="error"
        />
      </ModalContent>
    </Modal>
  );
};

export default MatchDeleteConfirmationModal;
