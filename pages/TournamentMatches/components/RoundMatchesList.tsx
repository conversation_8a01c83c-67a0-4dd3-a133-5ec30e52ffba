import React, { memo } from "react";
import { View } from "react-native";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import MatchCard from "./MatchCard";
import { type RoundMatches } from "../../../services/scheduleService";

interface RoundMatchesListProps {
  roundMatches: RoundMatches[];
  onMatchDeleted?: () => void;
}

const RoundHeader = memo<{ roundData: RoundMatches; isFirst?: boolean }>(
  ({ roundData, isFirst = false }) => (
    <View className={`mb-3 ${isFirst ? "mt-0" : "mt-6"}`}>
      <Text className="text-lg font-urbanistBold text-typography-900">
        {roundData.roundDisplayName}
      </Text>
    </View>
  )
);

RoundHeader.displayName = "RoundHeader";

const RoundMatchesList: React.FC<RoundMatchesListProps> = ({
  roundMatches,
  onMatchDeleted,
}) => {
  return (
    <VStack className="space-y-4">
      {roundMatches.map((roundData, index) => (
        <VStack key={roundData.roundName} className="space-y-2">
          <RoundHeader roundData={roundData} isFirst={index === 0} />
          <VStack className="space-y-2">
            {roundData.matches.map((match) => (
              <View pointerEvents="box-none" className="mb-2" key={match.id}>
                <MatchCard match={match} onMatchDeleted={onMatchDeleted} />
              </View>
            ))}
          </VStack>
        </VStack>
      ))}
    </VStack>
  );
};

export default memo(RoundMatchesList);
