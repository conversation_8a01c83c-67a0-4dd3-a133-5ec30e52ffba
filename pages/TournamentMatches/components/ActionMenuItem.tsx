import React, { memo } from "react";
import { Pressable, View } from "react-native";
import { Icon } from "@/components/ui/icon";
import { Text } from "@/components/ui/text";
import { ChevronRightIcon, LucideIcon } from "lucide-react-native";
import { Divider } from "@/components/ui/divider";

interface ActionMenuItemProps {
  icon: LucideIcon;
  label: string;
  onPress: () => void;
  textColor?: string;
  disabled?: boolean;
  showDivider?: boolean;
}

const ActionMenuItem: React.FC<ActionMenuItemProps> = ({
  icon,
  label,
  onPress,
  textColor = "text-typography-700",
  disabled = false,
  showDivider = false,
}) => {
  return (
    <View>
      <Pressable
        className="flex-row justify-between items-center py-4"
        onPress={disabled ? undefined : onPress}
        disabled={disabled}
      >
        <View className="flex-row items-center space-x-2 gap-3">
          <Icon
            as={icon}
            size="sm"
            className={disabled ? "text-gray-400" : textColor}
          />
          <Text
            className={`text-sm font-urbanistSemiBold ${
              disabled ? "text-gray-400" : textColor
            }`}
          >
            {label}
          </Text>
        </View>
        <Icon as={ChevronRightIcon} size="sm" className="text-typography-700" />
      </Pressable>
      {showDivider && <Divider className="w-full self-center" />}
    </View>
  );
};

export default memo(ActionMenuItem);
