import React, { memo } from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Icon } from "@/components/ui/icon";
import { Text } from "@/components/ui/text";
import { MapPinnedIcon } from "lucide-react-native";

interface CourtFieldDisplayProps {
  courtFieldNumber: string | null;
}

const CourtFieldDisplay: React.FC<CourtFieldDisplayProps> = ({
  courtFieldNumber,
}) => {
  if (!courtFieldNumber) return null;

  return (
    <VStack className="space-y-2 pt-2 mt-4 border-t border-gray-200 items-center">
      <HStack className="items-center space-x-2 gap-1">
        <Icon as={MapPinnedIcon} size="xs" className="text-typography-600" />
        <Text className="text-sm text-typography-600 font-urbanistMedium">
          {courtFieldNumber}
        </Text>
      </HStack>
    </VStack>
  );
};

export default memo(CourtFieldDisplay);
