import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { getMatchStatusBasedOnDate } from '@/services/matchStatusService';
import { formatDetailedDate, getTimeDifference } from '@/utils/dateTimeUtils';

dayjs.extend(relativeTime);

export function getMatchStatusText(scheduledDate: string | null, currentStatus: string): string {
  if (!scheduledDate || !dayjs(scheduledDate).isValid()) {
    return "Match date not available";
  }

  // Get the actual display status (including client-side delayed logic)
  const actualStatus = getMatchStatusBasedOnDate(scheduledDate, currentStatus);
  const { minutes: diffMinutes, hours: diffHours, days: diffDays, isPast } = getTimeDifference(scheduledDate);
  const formattedDate = formatDetailedDate(scheduledDate);

  switch (actualStatus) {
    case 'scheduled': {
      // Note: Past scheduled matches are automatically handled as 'delayed' by getMatchStatusBasedOnDate
      if (diffMinutes < 60) {
        return `Match starts in ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
      } else if (diffHours < 24) {
        return `Match starts in ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
      } else if (diffDays < 30) {
        return `Match scheduled for ${formattedDate}`;
      } else {
        return `Match scheduled for ${formattedDate}`;
      }
    }

    case 'delayed': {
      if (isPast) {
        if (diffMinutes < 60) {
          return `Match delayed by ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
        } else if (diffHours < 24) {
          return `Match delayed by ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
        } else if (diffDays < 30) {
          return `Match delayed by ${diffDays} day${diffDays > 1 ? 's' : ''}`;
        } else {
          return `Match delayed (originally scheduled for ${formattedDate})`;
        }
      } else {
        return `Match is delayed and rescheduled for ${formattedDate}`;
      }
    }

    case 'in_progress':
      return 'Match is live';

    case 'completed':
      return `Match completed on ${formattedDate}`;

    case 'cancelled':
      return 'Match has been cancelled';

    default:
      return `Match status: ${currentStatus}`;
  }
}
