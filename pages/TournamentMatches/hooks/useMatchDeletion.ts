import { useState } from 'react';
import { deleteMatch } from '@/services/matchService';
import { type Match } from '@/types/matches';

export const useMatchDeletion = () => {
  const [deleting, setDeleting] = useState(false);

  const handleDeleteMatch = async (match: Match): Promise<{ success: boolean; error?: string }> => {
    setDeleting(true);
    
    try {
      const result = await deleteMatch(match.id);
      
      if (result.success) {
        return { success: true };
      } else {
        return { 
          success: false, 
          error: result.error || 'Failed to delete match' 
        };
      }
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'An unexpected error occurred' 
      };
    } finally {
      setDeleting(false);
    }
  };

  return {
    deleting,
    handleDeleteMatch,
  };
};
