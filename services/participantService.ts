import {
  ParticipantType,
  ParticipantDetails,
  PARTICIPANT_TYPES,
  TeamParticipant,
  PlayerParticipant,
  PairParticipant,
} from '../types/participants';
import { fetchTeamById, fetchPairById } from '../services/teamsService';
import { fetchPlayerById } from '../services/playerService';
import { participantCache } from './participantCacheService';

interface ParticipantServiceConfig {
  fetch: (id: string) => Promise<any>;
  transform: (data: any) => ParticipantDetails;
  dataKey: string;
}

const participantServices: Record<ParticipantType, ParticipantServiceConfig> = {
  [PARTICIPANT_TYPES.TEAM]: {
    fetch: fetchTeamById,
    transform: (data: any): TeamParticipant => ({
      id: data.team.id,
      name: data.team.name,
      type: 'team',
      short_name: data.team.short_name,
      logo_url: data.team.logo_url,
      captain_id: data.team.captain_id,
    }),
    dataKey: 'team',
  },
  [PARTICIPANT_TYPES.PLAYER]: {
    fetch: fetchPlayerById,
    transform: (data: any): PlayerParticipant => ({
      id: data.player.id,
      name: data.player.name,
      type: 'player',
      jersey_number: data.player.jersey_number,
      team_id: data.player.team_id,
    }),
    dataKey: 'player',
  },
  [PARTICIPANT_TYPES.PAIR]: {
    fetch: fetchPairById,
    transform: (data: any): PairParticipant => ({
      id: data.data.id,
      name: data.data.name,
      type: 'pair',
      player_1_name: data.data.player_1_name,
      player_2_name: data.data.player_2_name,
      player_1_id: data.data.player_1_id,
      player_2_id: data.data.player_2_id,
    }),
    dataKey: 'data',
  },
};

/**
 * Create a fallback participant when API fetch fails
 */
export const createFallbackParticipant = (
  name: string,
  type: ParticipantType
): ParticipantDetails | null => {
  switch (type) {
    case PARTICIPANT_TYPES.TEAM:
      return { id: 'fallback', name, type: 'team' };

    case PARTICIPANT_TYPES.PLAYER:
      return { id: 'fallback', name, type: 'player' };

    case PARTICIPANT_TYPES.PAIR:
      const [player1, player2] = name.split(/\s*[/&]\s*/);
      return {
        id: 'fallback',
        name,
        type: 'pair',
        player_1_name: player1 || name,
        player_2_name: player2 || name,
      };

    default:
      return null;
  }
};

/**
 * Fetch participant by ID with caching support
 */
export const fetchParticipantById = async (
  participantId: string,
  participantType: ParticipantType,
  participantName?: string | null,
  enableCache: boolean = true,
  cacheExpiryMs?: number
): Promise<ParticipantDetails | null> => {
  // Check cache first
  if (enableCache) {
    const cachedData = participantCache.get(participantId, participantType, cacheExpiryMs);
    if (cachedData) {
      return cachedData;
    }
  }

  try {
    const service = participantServices[participantType];
    
    if (!service) {
      throw new Error(`Unknown participant type: ${participantType}`);
    }

    const result = await service.fetch(participantId);

    if (result?.success && result[service.dataKey as keyof typeof result]) {
      const transformedParticipant = service.transform(result);
      
      // Cache the result
      if (enableCache) {
        participantCache.set(participantId, participantType, transformedParticipant);
      }
      
      return transformedParticipant;
    } else {
      // Return fallback participant if fetch fails
      return participantName 
        ? createFallbackParticipant(participantName, participantType)
        : null;
    }
  } catch (err: any) {
    // Return fallback participant if fetch fails
    return participantName 
      ? createFallbackParticipant(participantName, participantType)
      : null;
  }
};
