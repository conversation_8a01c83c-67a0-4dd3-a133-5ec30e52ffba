import { ParticipantDetails, ParticipantType } from '../types/participants';
import { CACHE_SETTINGS } from '@/constants/matchConstants';

interface CacheEntry {
  data: ParticipantDetails;
  timestamp: number;
}

class ParticipantCacheService {
  private cache: { [key: string]: CacheEntry } = {};
  private defaultExpiryMs: number;

  constructor(expiryMs: number = CACHE_SETTINGS.DEFAULT_EXPIRY_MS) {
    this.defaultExpiryMs = expiryMs;
  }

  /**
   * Generate a cache key for a participant
   */
  private getCacheKey(id: string, type: ParticipantType): string {
    return `${type}_${id}`;
  }

  /**
   * Check if a cache entry is still valid
   */
  private isCacheValid(cacheKey: string, expiryMs?: number): boolean {
    const entry = this.cache[cacheKey];
    if (!entry) return false;
    
    const expiry = expiryMs || this.defaultExpiryMs;
    return Date.now() - entry.timestamp < expiry;
  }

  /**
   * Get participant from cache if valid
   */
  get(id: string, type: ParticipantType, expiryMs?: number): ParticipantDetails | null {
    const cacheKey = this.getCacheKey(id, type);
    
    if (!this.isCacheValid(cacheKey, expiryMs)) {
      return null;
    }
    
    return this.cache[cacheKey].data;
  }

  /**
   * Store participant in cache
   */
  set(id: string, type: ParticipantType, data: ParticipantDetails): void {
    const cacheKey = this.getCacheKey(id, type);
    this.cache[cacheKey] = {
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Clear expired entries from cache
   */
  clearExpired(expiryMs?: number): void {
    const expiry = expiryMs || this.defaultExpiryMs;
    const now = Date.now();
    
    Object.keys(this.cache).forEach(key => {
      if (now - this.cache[key].timestamp >= expiry) {
        delete this.cache[key];
      }
    });
  }

  /**
   * Clear all cache entries
   */
  clearAll(): void {
    this.cache = {};
  }

  /**
   * Get cache statistics
   */
  getStats(): { totalEntries: number; validEntries: number } {
    const totalEntries = Object.keys(this.cache).length;
    const validEntries = Object.keys(this.cache).filter(key => 
      this.isCacheValid(key)
    ).length;
    
    return { totalEntries, validEntries };
  }
}

// Export a singleton instance
export const participantCache = new ParticipantCacheService();
