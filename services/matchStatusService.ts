/**
 * Client-side match status utilities
 * Handles delayed status logic without backend storage
 */

import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { formatDetailedDate, getTimeDifference } from '@/utils/dateTimeUtils';

dayjs.extend(relativeTime);

/**
 * Check if a specific match should be marked as delayed
 * This is a client-side utility function
 */
export function shouldMatchBeDelayed(scheduledDate: string | null, currentStatus: string): boolean {
  if (!scheduledDate || currentStatus !== 'scheduled') {
    return false;
  }

  const now = new Date();
  const matchDate = new Date(scheduledDate);
  
  return matchDate < now;
}

/**
 * Get the appropriate status for a match based on its scheduled date
 * Returns 'delayed' for client-side display if scheduled time has passed
 */
export function getMatchStatusBasedOnDate(scheduledDate: string | null, currentStatus: string): string {
  if (shouldMatchBeDelayed(scheduledDate, currentStatus)) {
    return 'delayed';
  }
  return currentStatus;
}

/**
 * Get display text for match status including client-side delayed status
 */
export function getStatusDisplayText(status: string): string {
  switch (status) {
    case 'scheduled':
      return 'Scheduled';
    case 'delayed':
      return 'Delayed';
    case 'in_progress':
      return 'Live';
    case 'completed':
      return 'Completed';
    case 'cancelled':
      return 'Cancelled';
    default:
      return status?.toUpperCase() || 'Unknown';
  }
}

/**
 * Get color class for match status including client-side delayed status
 */
export function getStatusColor(status: string): string {
  switch (status) {
    case 'scheduled':
      return 'bg-blue-500';
    case 'delayed':
      return 'bg-orange-500';
    case 'in_progress':
      return 'bg-green-500';
    case 'completed':
      return 'bg-gray-500';
    case 'cancelled':
      return 'bg-red-500';
    default:
      return 'bg-gray-400';
  }
}

/**
 * Get detailed status text for a match with time-based context
 */
export function getMatchStatusText(scheduledDate: string | null, currentStatus: string): string {
  if (!scheduledDate || !dayjs(scheduledDate).isValid()) {
    return "Match date not available";
  }

  // Get the actual display status (including client-side delayed logic)
  const actualStatus = getMatchStatusBasedOnDate(scheduledDate, currentStatus);
  const { minutes: diffMinutes, hours: diffHours, days: diffDays, isPast } = getTimeDifference(scheduledDate);
  const formattedDate = formatDetailedDate(scheduledDate);

  switch (actualStatus) {
    case 'scheduled': {
      // Note: Past scheduled matches are automatically handled as 'delayed' by getMatchStatusBasedOnDate
      if (diffMinutes < 60) {
        return `Match starts in ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
      } else if (diffHours < 24) {
        return `Match starts in ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
      } else if (diffDays < 30) {
        return `Match scheduled for ${formattedDate}`;
      } else {
        return `Match scheduled for ${formattedDate}`;
      }
    }

    case 'delayed': {
      if (isPast) {
        if (diffMinutes < 60) {
          return `Match delayed by ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
        } else if (diffHours < 24) {
          return `Match delayed by ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
        } else if (diffDays < 30) {
          return `Match delayed by ${diffDays} day${diffDays > 1 ? 's' : ''}`;
        } else {
          return `Match delayed (originally scheduled for ${formattedDate})`;
        }
      } else {
        return `Match is delayed and rescheduled for ${formattedDate}`;
      }
    }

    case 'in_progress':
      return 'Match is live';

    case 'completed':
      return `Match completed on ${formattedDate}`;

    case 'cancelled':
      return 'Match has been cancelled';

    default:
      return `Match status: ${currentStatus}`;
  }
}
