import dayjs from "dayjs";
import relativeTime from 'dayjs/plugin/relativeTime';
import { DATE_FORMATS } from "@/constants/matchConstants";

dayjs.extend(relativeTime);

/**
 * Format a date string for display
 */
export const formatDate = (dateTime: string | null): string => {
  if (!dateTime) return "TBD";

  try {
    return dayjs(dateTime).format(DATE_FORMATS.DISPLAY_DATE);
  } catch (error) {
    return "Invalid Date";
  }
};

/**
 * Format a time string for display
 */
export const formatTime = (dateTime: string | null): string | null => {
  if (!dateTime) return null;

  try {
    return dayjs(dateTime).format(DATE_FORMATS.DISPLAY_TIME);
  } catch (error) {
    return "Invalid Time";
  }
};

/**
 * Format a date for detailed display (e.g., "Jul 25th, 2025")
 */
export const formatDetailedDate = (dateTime: string | null): string => {
  if (!dateTime) return "Date not available";

  try {
    return dayjs(dateTime).format(DATE_FORMATS.DETAILED_DATE);
  } catch (error) {
    return "Invalid Date";
  }
};

/**
 * Check if a date string is valid
 */
export const isValidDate = (dateTime: string | null): boolean => {
  if (!dateTime) return false;
  return dayjs(dateTime).isValid();
};

/**
 * Get relative time difference between two dates
 */
export const getTimeDifference = (targetDate: string, referenceDate?: string) => {
  const target = dayjs(targetDate);
  const reference = referenceDate ? dayjs(referenceDate) : dayjs();
  
  const diffMinutes = Math.abs(target.diff(reference, 'minute'));
  const diffHours = Math.abs(target.diff(reference, 'hour'));
  const diffDays = Math.abs(target.diff(reference, 'day'));
  
  return {
    minutes: diffMinutes,
    hours: diffHours,
    days: diffDays,
    isPast: target.isBefore(reference),
  };
};
