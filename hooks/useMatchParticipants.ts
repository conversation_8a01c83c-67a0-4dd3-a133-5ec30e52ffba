import { useState, useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import {
  ParticipantType,
  ParticipantDetails,
} from '../types/participants';
import { fetchParticipantById, createFallbackParticipant } from '../services/participantService';
import { CACHE_SETTINGS } from '@/constants/matchConstants';

interface UseMatchParticipantsProps {
  participant1Id: string | null;
  participant2Id: string | null;
  participantType: ParticipantType;
  participant1Name?: string | null;
  participant2Name?: string | null;
  enableCache?: boolean;
  cacheExpiryMs?: number;
}

interface UseMatchParticipantsReturn {
  participant1: ParticipantDetails | null;
  participant2: ParticipantDetails | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useMatchParticipants = ({
  participant1Id,
  participant2Id,
  participantType,
  participant1Name,
  participant2Name,
  enableCache = true,
  cacheExpiryMs = CACHE_SETTINGS.DEFAULT_EXPIRY_MS,
}: UseMatchParticipantsProps): UseMatchParticipantsReturn => {
  const [participant1, setParticipant1] = useState<ParticipantDetails | null>(null);
  const [participant2, setParticipant2] = useState<ParticipantDetails | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchParticipantDetails = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const promises: Promise<ParticipantDetails | null>[] = [];

      // Fetch participant 1
      if (participant1Id) {
        promises.push(
          fetchParticipantById(
            participant1Id,
            participantType,
            participant1Name,
            enableCache,
            cacheExpiryMs
          )
        );
      } else {
        promises.push(
          Promise.resolve(
            participant1Name
              ? createFallbackParticipant(participant1Name, participantType)
              : null
          )
        );
      }

      // Fetch participant 2
      if (participant2Id) {
        promises.push(
          fetchParticipantById(
            participant2Id,
            participantType,
            participant2Name,
            enableCache,
            cacheExpiryMs
          )
        );
      } else {
        promises.push(
          Promise.resolve(
            participant2Name
              ? createFallbackParticipant(participant2Name, participantType)
              : null
          )
        );
      }

      const [p1, p2] = await Promise.all(promises);

      setParticipant1(p1);
      setParticipant2(p2);
    } catch (err: any) {
      setError('Failed to fetch participant details');
      // Set fallback participants
      setParticipant1(
        participant1Name
          ? createFallbackParticipant(participant1Name, participantType)
          : null
      );
      setParticipant2(
        participant2Name
          ? createFallbackParticipant(participant2Name, participantType)
          : null
      );
    } finally {
      setLoading(false);
    }
  }, [
    participant1Id,
    participant2Id,
    participantType,
    participant1Name,
    participant2Name,
    enableCache,
    cacheExpiryMs,
  ]);

  useFocusEffect(
    useCallback(() => {
      fetchParticipantDetails();
    }, [fetchParticipantDetails])
  );

  return {
    participant1,
    participant2,
    loading,
    error,
    refetch: fetchParticipantDetails,
  };
};
